import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';

/// 部门树节点数据模型
class DepartmentTreeNode {
  final DepartmentModel department;
  final List<DepartmentTreeNode> children;
  bool isExpanded;
  bool isSelected; // 节点点击选择状态（高亮显示）
  bool isChecked; // 复选框选中状态
  bool isIndeterminate; // 复选框半选状态

  DepartmentTreeNode({
    required this.department,
    List<DepartmentTreeNode>? children,
    this.isExpanded = false,
    this.isSelected = false,
    this.isChecked = false,
    this.isIndeterminate = false,
  }) : children = children ?? [];

  /// 检查是否为叶子节点
  bool get isLeaf => children.isEmpty;

  /// 获取节点层级深度
  int get level {
    if (department.parentIdList.isEmpty) return 0;
    return department.parentIdList.length;
  }

  /// 获取所有被选中的子节点数量
  int get checkedChildrenCount {
    return children.where((child) => child.isChecked).length;
  }

  /// 获取所有子节点数量
  int get totalChildrenCount {
    return children.length;
  }

  /// 检查是否有部分子节点被选中
  bool get hasPartiallyCheckedChildren {
    if (children.isEmpty) return false;
    int checkedCount = checkedChildrenCount;
    return checkedCount > 0 && checkedCount < totalChildrenCount;
  }

  /// 检查是否所有子节点都被选中
  bool get hasAllChildrenChecked {
    if (children.isEmpty) return false;
    return checkedChildrenCount == totalChildrenCount;
  }
}

/// 部门选择器组件
class DepartmentSelector extends StatefulWidget {
  /// 是否显示复选框
  final bool showCheckbox;

  /// 节点点击回调
  final void Function(DepartmentModel department)? onNodeTap;

  /// 节点选择回调（复选框点击时触发）
  final void Function(DepartmentModel department, bool isSelected)? onNodeSelected;

  const DepartmentSelector({
    super.key,
    this.showCheckbox = false,
    this.onNodeTap,
    this.onNodeSelected,
  });

  @override
  State<DepartmentSelector> createState() => DepartmentSelectorState();
}

// 将 State 类改为公开，以便外部可以访问
class DepartmentSelectorState extends State<DepartmentSelector> {
  final reqParams = {'PageIndex': 1, 'PageSize': 9999};
  PagesModel<DepartmentModel> _pages = PagesModel();
  List<DepartmentModel> _list = [];
  List<DepartmentTreeNode> _treeNodes = [];

  @override
  void initState() {
    super.initState();
    getList();
  }

  /// 刷新部门列表数据 - 公开方法供外部调用
  void refresh() {
    getList();
  }

  /// 获取部门列表数据
  void getList() {
    DepartmentApi.getList(reqParams).then((value) {
      setState(() {
        _pages = PagesModel.fromJson(
          value,
          (json) => DepartmentModel.fromJson(json as Map<String, dynamic>),
        );
        _list = _pages.items;
        _buildTreeStructure();
      });
    });
  }

  /// 构建树形结构
  void _buildTreeStructure() {
    // 创建ID到节点的映射
    Map<String, DepartmentTreeNode> nodeMap = {};

    // 首先创建所有节点，为每个节点创建可变的children列表
    for (var dept in _list) {
      if (dept.id != null) {
        nodeMap[dept.id!] = DepartmentTreeNode(
          department: dept,
          children: <DepartmentTreeNode>[], // 创建可变列表
        );
      }
    }

    // 构建父子关系
    List<DepartmentTreeNode> rootNodes = [];

    for (var dept in _list) {
      if (dept.id == null) continue;

      var currentNode = nodeMap[dept.id!]!;

      if (dept.parentId == null || dept.parentId!.isEmpty) {
        // 根节点
        rootNodes.add(currentNode);
        // 默认展开第一个根节点
        if (rootNodes.length == 1) {
          currentNode.isExpanded = true;
          currentNode.isSelected = true;
          _handleNodeTap(currentNode);
        }
      } else {
        // 子节点
        var parentNode = nodeMap[dept.parentId];
        if (parentNode != null) {
          parentNode.children.add(currentNode);
        }
      }
    }

    _treeNodes = rootNodes;
  }

  /// 处理节点点击事件（仅影响高亮显示，不影响复选框状态）
  void _handleNodeTap(DepartmentTreeNode node) {
    setState(() {
      // 如果有之前选中的节点，取消选中（仅影响高亮显示）
      _clearAllHighlightSelections(_treeNodes);
      // 选中当前节点（仅高亮显示）
      node.isSelected = true;
    });

    // 触发回调
    widget.onNodeTap?.call(node.department);
  }

  /// 处理节点展开/折叠
  void _handleNodeToggle(DepartmentTreeNode node) {
    setState(() {
      node.isExpanded = !node.isExpanded;
    });
  }

  /// 处理复选框选择
  void _handleNodeCheckboxChanged(DepartmentTreeNode node, bool? value) {
    setState(() {
      bool newCheckedState = value ?? false;

      // 设置当前节点的选中状态
      node.isChecked = newCheckedState;
      node.isIndeterminate = false; // 直接操作时清除半选状态

      // 级联选择子节点
      _cascadeCheckChildren(node, newCheckedState);

      // 更新所有父节点的状态
      _updateParentCheckStates();
    });

    // 触发回调，传递当前节点的选中状态
    widget.onNodeSelected?.call(node.department, node.isChecked);
  }

  /// 级联选择子节点
  void _cascadeCheckChildren(DepartmentTreeNode node, bool checked) {
    for (var child in node.children) {
      child.isChecked = checked;
      child.isIndeterminate = false;
      // 递归处理子节点的子节点
      _cascadeCheckChildren(child, checked);
    }
  }

  /// 更新所有父节点的选中状态
  void _updateParentCheckStates() {
    _updateNodeParentStates(_treeNodes);
  }

  /// 递归更新节点的父级状态
  void _updateNodeParentStates(List<DepartmentTreeNode> nodes) {
    for (var node in nodes) {
      if (node.children.isNotEmpty) {
        // 计算子节点状态
        int checkedCount = 0;
        int indeterminateCount = 0;

        for (var child in node.children) {
          if (child.isChecked) {
            checkedCount++;
          } else if (child.isIndeterminate) {
            indeterminateCount++;
          }
        }

        // 更新当前节点状态
        if (checkedCount == node.children.length) {
          // 所有子节点都被选中
          node.isChecked = true;
          node.isIndeterminate = false;
        } else if (checkedCount > 0 || indeterminateCount > 0) {
          // 部分子节点被选中或有半选状态
          node.isChecked = false;
          node.isIndeterminate = true;
        } else {
          // 没有子节点被选中
          node.isChecked = false;
          node.isIndeterminate = false;
        }

        // 递归处理子节点
        _updateNodeParentStates(node.children);
      }
    }
  }

  /// 获取所有被选中的部门
  List<DepartmentModel> _getAllCheckedDepartments() {
    List<DepartmentModel> checkedDepartments = [];
    _collectCheckedDepartments(_treeNodes, checkedDepartments);
    return checkedDepartments;
  }

  /// 递归收集被选中的部门
  void _collectCheckedDepartments(List<DepartmentTreeNode> nodes, List<DepartmentModel> result) {
    for (var node in nodes) {
      if (node.isChecked) {
        result.add(node.department);
      }
      if (node.children.isNotEmpty) {
        _collectCheckedDepartments(node.children, result);
      }
    }
  }

  /// 清除所有高亮选择状态（不影响复选框状态）
  void _clearAllHighlightSelections(List<DepartmentTreeNode> nodes) {
    for (var node in nodes) {
      node.isSelected = false; // 只清除高亮状态，不影响isChecked
      if (node.children.isNotEmpty) {
        _clearAllHighlightSelections(node.children);
      }
    }
  }

  /// 获取所有被选中的部门（公开方法，供外部调用）
  List<DepartmentModel> getAllCheckedDepartments() {
    return _getAllCheckedDepartments();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(right: BorderSide(color: context.border300, width: 1)),
      ),
      child:
          _list.isEmpty
              ? const Center(child: CircularProgressIndicator())
              : ListView(padding: const EdgeInsets.all(8.0), children: _buildTreeNodes(_treeNodes)),
    );
  }

  /// 构建树节点列表
  List<Widget> _buildTreeNodes(List<DepartmentTreeNode> nodes) {
    List<Widget> widgets = [];

    for (var node in nodes) {
      widgets.add(_buildTreeNodeWidget(node));

      // 如果节点展开且有子节点，递归构建子节点
      if (node.isExpanded && node.children.isNotEmpty) {
        widgets.addAll(_buildTreeNodes(node.children));
      }
    }

    return widgets;
  }

  /// 构建单个树节点组件
  Widget _buildTreeNodeWidget(DepartmentTreeNode node) {
    final int indentLevel = node.level;
    final bool hasChildren = node.children.isNotEmpty;
    final bool isSelected = node.isSelected;

    return Container(
      margin: EdgeInsets.only(
        left: indentLevel * 20.0, // 根据层级缩进
        bottom: 2.0,
      ),
      child: Material(
        color: isSelected ? context.background200 : Colors.transparent,
        borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
        child: InkWell(
          onTap: () => _handleNodeTap(node),
          hoverColor: context.activeGrayColor.withValues(alpha: 0.5),
          splashColor: context.activeGrayColor,
          borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
          child: Container(
            height: 36.0, // 固定行高，确保所有节点高度一致
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                // 展开/折叠图标区域 - 固定宽度确保对齐
                SizedBox(
                  width: 20,
                  height: 20,
                  child:
                      hasChildren
                          ? GestureDetector(
                            onTap: () => _handleNodeToggle(node),
                            child: Center(
                              child: AnimatedRotation(
                                turns: node.isExpanded ? 0.5 : 0,
                                duration: const Duration(milliseconds: 300),
                                child: Icon(
                                  IconFont.mianxing_xiala,
                                  size: 16,
                                  color: context.icon100,
                                ),
                              ),
                            ),
                          )
                          : null, // 叶子节点不显示图标，但保持占位空间
                ),

                const SizedBox(width: 8),

                // 复选框（可选）- 固定高度，支持三种状态
                if (widget.showCheckbox) ...[
                  SizedBox(
                    height: 20,
                    child: Checkbox(
                      value: node.isIndeterminate ? null : node.isChecked,
                      tristate: true, // 启用三态支持
                      onChanged: (value) => _handleNodeCheckboxChanged(node, value),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],

                // 部门名称 - 垂直居中对齐
                Expanded(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      node.department.departmentName,
                      style: TextStyle(
                        color: isSelected ? context.textPrimary : context.textSecondary,
                        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        fontSize: 14, // 固定字体大小
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1, // 确保单行显示
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
