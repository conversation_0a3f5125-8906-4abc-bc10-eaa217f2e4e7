# DepartmentSelector 组件文档

## 组件概述

`DepartmentSelector` 是一个功能完整的部门选择器组件，以树形结构展示组织架构中的部门层级关系。该组件支持单选和多选模式，提供了丰富的交互功能，包括节点展开/折叠、高亮选择、复选框级联选择等。

### 主要特性

- 🌳 **树形结构展示**：自动构建部门层级关系，支持无限层级嵌套
- ✅ **多种选择模式**：支持单选（高亮）和多选（复选框）模式
- 🔄 **级联选择**：复选框模式下支持父子节点级联选择，包含半选状态
- 🎨 **视觉反馈**：提供展开/折叠动画、悬停效果和选中状态高亮
- 📱 **响应式设计**：适配不同屏幕尺寸，支持触摸和鼠标操作
- 🔄 **数据刷新**：支持动态刷新部门数据

## API 文档

### 构造函数

```dart
DepartmentSelector({
  Key? key,
  bool showCheckbox = false,
  void Function(DepartmentModel department)? onNodeTap,
  void Function(DepartmentModel department, bool isSelected)? onNodeSelected,
})
```

### 构造函数参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `key` | `Key?` | `null` | Widget 的唯一标识符 |
| `showCheckbox` | `bool` | `false` | 是否显示复选框，启用多选模式 |
| `onNodeTap` | `Function(DepartmentModel)?` | `null` | 节点点击回调，用于处理单选逻辑 |
| `onNodeSelected` | `Function(DepartmentModel, bool)?` | `null` | 节点复选框状态变化回调 |

### 公共方法

#### DepartmentSelectorState 类方法

```dart
// 刷新部门数据
void refresh()

// 获取所有被选中的部门（复选框模式）
List<DepartmentModel> getAllCheckedDepartments()
```

### 回调函数说明

#### onNodeTap
```dart
void Function(DepartmentModel department)? onNodeTap
```
- **触发时机**：用户点击部门节点时
- **参数**：`department` - 被点击的部门模型
- **用途**：处理单选逻辑，通常用于高亮显示选中的部门

#### onNodeSelected
```dart
void Function(DepartmentModel department, bool isSelected)? onNodeSelected
```
- **触发时机**：用户点击复选框时
- **参数**：
  - `department` - 状态发生变化的部门模型
  - `isSelected` - 当前复选框的选中状态
- **用途**：处理多选逻辑，响应复选框状态变化

## 使用示例

### 基本用法

```dart
// 简单的部门选择器，仅支持单选
DepartmentSelector(
  onNodeTap: (department) {
    print('选中部门: ${department.departmentName}');
  },
)
```

### 启用复选框模式

```dart
// 支持多选的部门选择器
DepartmentSelector(
  showCheckbox: true,
  onNodeTap: (department) {
    // 处理节点点击（高亮显示）
    setState(() {
      selectedDepartment = department;
    });
  },
  onNodeSelected: (department, isSelected) {
    // 处理复选框状态变化
    print('部门 ${department.departmentName} ${isSelected ? '被选中' : '取消选中'}');
  },
)
```

### 完整使用示例

```dart
class DepartmentManagementPage extends StatefulWidget {
  @override
  State<DepartmentManagementPage> createState() => _DepartmentManagementPageState();
}

class _DepartmentManagementPageState extends State<DepartmentManagementPage> {
  final GlobalKey<DepartmentSelectorState> _selectorKey = GlobalKey<DepartmentSelectorState>();
  DepartmentModel? _selectedDepartment;
  List<DepartmentModel> _checkedDepartments = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // 部门选择器
          SizedBox(
            width: 300,
            child: DepartmentSelector(
              key: _selectorKey,
              showCheckbox: true,
              onNodeTap: (department) {
                setState(() {
                  _selectedDepartment = department;
                });
              },
              onNodeSelected: (department, isSelected) {
                // 实时更新选中的部门列表
                _updateCheckedDepartments();
              },
            ),
          ),
          
          // 操作区域
          Expanded(
            child: Column(
              children: [
                ElevatedButton(
                  onPressed: () {
                    // 获取所有选中的部门
                    final selected = _selectorKey.currentState?.getAllCheckedDepartments() ?? [];
                    setState(() {
                      _checkedDepartments = selected;
                    });
                  },
                  child: Text('获取选中部门'),
                ),
                
                ElevatedButton(
                  onPressed: () {
                    // 刷新部门数据
                    _selectorKey.currentState?.refresh();
                  },
                  child: Text('刷新数据'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _updateCheckedDepartments() {
    final selected = _selectorKey.currentState?.getAllCheckedDepartments() ?? [];
    setState(() {
      _checkedDepartments = selected;
    });
  }
}
```

## 属性说明

### DepartmentSelector 属性

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `showCheckbox` | `bool` | `false` | 控制是否显示复选框，启用多选功能 |
| `onNodeTap` | `Function?` | `null` | 节点点击回调，用于单选模式 |
| `onNodeSelected` | `Function?` | `null` | 复选框状态变化回调，用于多选模式 |

### DepartmentTreeNode 属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `department` | `DepartmentModel` | 关联的部门数据模型 |
| `children` | `List<DepartmentTreeNode>` | 子节点列表 |
| `isExpanded` | `bool` | 节点是否展开 |
| `isSelected` | `bool` | 节点是否被高亮选中 |
| `isChecked` | `bool` | 复选框是否被选中 |
| `isIndeterminate` | `bool` | 复选框是否处于半选状态 |

### DepartmentModel 主要属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| `id` | `String?` | 部门唯一标识符 |
| `departmentName` | `String` | 部门名称 |
| `parentId` | `String?` | 父部门ID |
| `parentIdList` | `List<String>` | 父级部门ID链表，用于确定层级深度 |
| `description` | `String` | 部门描述 |
| `departmentHead` | `String?` | 部门负责人 |

## 注意事项

### 使用限制

1. **数据依赖**：组件依赖 `DepartmentApi.getList()` 方法获取部门数据，确保 API 接口正常可用
2. **状态管理**：如需访问组件内部方法，必须使用 `GlobalKey<DepartmentSelectorState>` 获取状态实例
3. **性能考虑**：大量部门数据时建议启用分页或虚拟滚动优化

### 最佳实践

1. **键值管理**：为组件设置唯一的 `GlobalKey`，便于外部调用方法
2. **回调处理**：合理使用 `onNodeTap` 和 `onNodeSelected` 回调，避免重复处理
3. **状态同步**：在复选框模式下，及时调用 `getAllCheckedDepartments()` 同步选中状态
4. **数据刷新**：部门数据变更后调用 `refresh()` 方法更新显示

### 常见问题

1. **无法获取选中部门**：确保使用 `GlobalKey` 并在组件初始化完成后调用方法
2. **复选框状态异常**：检查是否正确设置 `showCheckbox: true`
3. **层级显示错误**：确认 `DepartmentModel` 中的 `parentId` 和 `parentIdList` 数据正确

### 样式定制

组件使用主题色彩系统，支持以下自定义：
- 选中状态背景色：`context.background200`
- 文本颜色：`context.textPrimary` / `context.textSecondary`
- 图标颜色：`context.icon100`
- 边框颜色：`context.border300`

### 依赖要求

- Flutter SDK
- `octasync_client/api/department.dart` - 部门 API 接口
- `octasync_client/models/department/department_model.dart` - 部门数据模型
- `octasync_client/models/pages_model/pages_model.dart` - 分页数据模型
